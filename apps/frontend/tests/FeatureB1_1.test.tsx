import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import FeatureB1_1 from '../../../frontend/features/feature/featureB1_1';

describe('FeatureB1_1 矩阵功能组件', () => {
  test('正确渲染矩阵文本', () => {
    render(<FeatureB1_1 />);
    expect(screen.getByText('矩阵')).toBeInTheDocument();
  });

  test('正确渲染初始化和坐标按键', () => {
    render(<FeatureB1_1 />);
    expect(screen.getByText('初始化')).toBeInTheDocument();
    expect(screen.getByText('坐标')).toBeInTheDocument();
  });

  test('初始化按键可以被点击', () => {
    render(<FeatureB1_1 />);
    const initButton = screen.getByText('初始化');
    fireEvent.click(initButton);
    expect(initButton).toBeInTheDocument();
  });

  test('坐标按键可以切换状态', () => {
    render(<FeatureB1_1 />);
    const coordButton = screen.getByText('坐标');
    fireEvent.click(coordButton);
    expect(coordButton).toBeInTheDocument();
  });

  test('组件具有正确的样式结构', () => {
    const { container } = render(<FeatureB1_1 />);
    const mainDiv = container.firstChild as HTMLElement;
    
    expect(mainDiv).toHaveStyle({
      position: 'relative',
      backgroundColor: '#bebebe'
    });
  });
});
