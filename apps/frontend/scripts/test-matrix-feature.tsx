import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import FeatureB1_1 from '../../../frontend/features/feature/featureB1_1';

// 测试矩阵功能组件
describe('FeatureB1_1 Matrix Component', () => {
  test('renders matrix feature with correct text', () => {
    render(<FeatureB1_1 />);
    
    // 检查是否显示"矩阵"文本
    expect(screen.getByText('矩阵')).toBeInTheDocument();
  });

  test('renders initialize and coordinate buttons', () => {
    render(<FeatureB1_1 />);
    
    // 检查是否显示初始化按键
    expect(screen.getByText('初始化')).toBeInTheDocument();
    
    // 检查是否显示坐标按键
    expect(screen.getByText('坐标')).toBeInTheDocument();
  });

  test('initialize button triggers matrix initialization', () => {
    render(<FeatureB1_1 />);
    
    const initializeButton = screen.getByText('初始化');
    
    // 模拟点击初始化按键
    fireEvent.click(initializeButton);
    
    // 验证按键可以被点击（不会抛出错误）
    expect(initializeButton).toBeInTheDocument();
  });

  test('coordinate button toggles state', () => {
    render(<FeatureB1_1 />);
    
    const coordinateButton = screen.getByText('坐标');
    
    // 模拟点击坐标按键
    fireEvent.click(coordinateButton);
    
    // 验证按键可以被点击（不会抛出错误）
    expect(coordinateButton).toBeInTheDocument();
  });
});
