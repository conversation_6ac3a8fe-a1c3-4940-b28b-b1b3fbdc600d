import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ComponentA1 from '../../../frontend/componets/componet/componetA1';
import { useMatrixStore } from '../../../frontend/Store/store';

// 测试矩阵系统基本功能
describe('Matrix System (三级容器)', () => {
  beforeEach(() => {
    // 重置状态
    useMatrixStore.getState().initialize();
  });

  test('应该渲染33x33网格', () => {
    render(<ComponentA1 />);
    
    // 验证容器存在
    const container = screen.getByRole('generic');
    expect(container).toBeInTheDocument();
  });

  test('坐标显示功能', () => {
    const { setCoordinateDisplay } = useMatrixStore.getState();
    
    render(<ComponentA1 />);
    
    // 启用坐标显示
    setCoordinateDisplay(true);
    
    // 验证坐标显示状态
    expect(useMatrixStore.getState().coordinateDisplay).toBe(true);
  });

  test('组件激活状态切换', () => {
    const { toggleComponentActivation, activatedComponents } = useMatrixStore.getState();
    
    // 激活一个组件
    toggleComponentActivation('0,0');
    
    // 验证组件被激活
    expect(useMatrixStore.getState().activatedComponents.has('0,0')).toBe(true);
    
    // 再次切换应该取消激活
    toggleComponentActivation('0,0');
    expect(useMatrixStore.getState().activatedComponents.has('0,0')).toBe(false);
  });

  test('高亮边框互斥单选', () => {
    const { setHighlightedComponent } = useMatrixStore.getState();
    
    // 设置第一个组件高亮
    setHighlightedComponent('0,0');
    expect(useMatrixStore.getState().highlightedComponent).toBe('0,0');
    
    // 设置第二个组件高亮，应该替换第一个
    setHighlightedComponent('1,0');
    expect(useMatrixStore.getState().highlightedComponent).toBe('1,0');
  });

  test('初始化功能', () => {
    const { initialize, activatedComponents, highlightedComponent } = useMatrixStore.getState();
    
    // 先设置一些状态
    useMatrixStore.getState().toggleComponentActivation('0,0');
    useMatrixStore.getState().setHighlightedComponent('1,0');
    
    // 执行初始化
    initialize();
    
    // 验证状态被重置
    expect(useMatrixStore.getState().activatedComponents.size).toBe(0);
    expect(useMatrixStore.getState().highlightedComponent).toBe(null);
    expect(useMatrixStore.getState().isInitialized).toBe(true);
  });
});

console.log('矩阵系统测试脚本已创建');
console.log('测试覆盖功能：');
console.log('1. 33x33网格渲染');
console.log('2. 坐标显示切换');
console.log('3. 组件激活状态管理');
console.log('4. 高亮边框互斥选择');
console.log('5. 系统初始化功能');
