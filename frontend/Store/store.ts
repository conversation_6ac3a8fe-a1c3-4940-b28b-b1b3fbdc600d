// Zustand 状态管理文件
// 此文件用于管理应用的全局状态

import { create } from 'zustand';

interface ContainerState {
  activeContainer: 'mode' | 'business';
  setActiveContainer: (container: 'mode' | 'business') => void;
}

interface MatrixState {
  // 坐标按键的状态
  coordinateMode: boolean;
  setCoordinateMode: (mode: boolean) => void;
  // 初始化触发器
  initializeTrigger: number;
  initializeMatrix: () => void;
}

export const useContainerStore = create<ContainerState>((set) => ({
  activeContainer: 'mode', // 默认显示模式容器
  setActiveContainer: (container) => set({ activeContainer: container }),
}));

export const useMatrixStore = create<MatrixState>((set, get) => ({
  // 初始状态
  coordinateMode: false,
  initializeTrigger: 0,

  // 设置坐标模式
  setCoordinateMode: (mode: boolean) => {
    set({ coordinateMode: mode });
  },

  // 初始化矩阵
  initializeMatrix: () => {
    // 重置坐标按键状态为false
    set({
      coordinateMode: false,
      initializeTrigger: get().initializeTrigger + 1 // 触发重新渲染
    });
    // 发出初始化事件
    console.log('矩阵初始化完成');
  }
}));
