// Zustand 状态管理文件
// 此文件用于管理应用的全局状态

import { create } from 'zustand';

interface ContainerState {
  activeContainer: 'mode' | 'business';
  setActiveContainer: (container: 'mode' | 'business') => void;
}

export const useContainerStore = create<ContainerState>((set) => ({
  activeContainer: 'mode', // 默认显示模式容器
  setActiveContainer: (container) => set({ activeContainer: container }),
}));

// 矩阵系统状态管理
interface MatrixState {
  // 坐标按键状态
  coordinateDisplay: boolean;
  setCoordinateDisplay: (display: boolean) => void;

  // 初始化事件
  isInitialized: boolean;
  initialize: () => void;

  // 组件激活状态 (33x33网格)
  activatedComponents: Set<string>;
  toggleComponentActivation: (coordinate: string) => void;

  // 高亮边框状态 (互斥单选)
  highlightedComponent: string | null;
  setHighlightedComponent: (coordinate: string | null) => void;
}

export const useMatrixStore = create<MatrixState>((set, get) => ({
  coordinateDisplay: false,
  setCoordinateDisplay: (display) => set({ coordinateDisplay: display }),

  isInitialized: false,
  initialize: () => set({
    isInitialized: true,
    activatedComponents: new Set(),
    highlightedComponent: null
  }),

  activatedComponents: new Set(),
  toggleComponentActivation: (coordinate) => {
    const { activatedComponents } = get();
    const newSet = new Set(activatedComponents);
    if (newSet.has(coordinate)) {
      newSet.delete(coordinate);
    } else {
      newSet.add(coordinate);
    }
    set({ activatedComponents: newSet });
  },

  highlightedComponent: null,
  setHighlightedComponent: (coordinate) => set({ highlightedComponent: coordinate }),
}));
