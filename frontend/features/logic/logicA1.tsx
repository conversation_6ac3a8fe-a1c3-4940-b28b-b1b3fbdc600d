'use client'

import React, { useEffect, useState } from 'react';
import { useMatrixStore } from '../../Store/store';
import FeatureA1 from '../feature/featureA1';

interface ComponentState {
  activated: boolean;
  highlighted: boolean;
  coordinates: string;
}

const LogicA1: React.FC = () => {
  const { coordinateMode, initializeTrigger } = useMatrixStore();
  const [components, setComponents] = useState<ComponentState[]>([]);
  const [highlightedIndex, setHighlightedIndex] = useState<number | null>(null);

  // 初始化33x33网格组件
  useEffect(() => {
    const initialComponents: ComponentState[] = [];
    for (let row = 0; row < 33; row++) {
      for (let col = 0; col < 33; col++) {
        // 计算坐标，以中心为原点(0,0)
        const x = col - 16; // 中心列为16
        const y = 16 - row; // 中心行为16，y轴向上为正
        initialComponents.push({
          activated: false,
          highlighted: false,
          coordinates: `${x},${y}`
        });
      }
    }
    setComponents(initialComponents);
  }, []);

  // 监听初始化事件
  useEffect(() => {
    const resetComponents = () => {
      setComponents(prev => prev.map(comp => ({
        ...comp,
        activated: false,
        highlighted: false
      })));
      setHighlightedIndex(null);
    };

    // 当initializeTrigger变化时重置组件状态
    if (initializeTrigger > 0) {
      resetComponents();
    }
  }, [initializeTrigger]);

  // 处理组件点击
  const handleComponentClick = (index: number) => {
    // 切换激活状态
    setComponents(prev => prev.map((comp, i) => 
      i === index ? { ...comp, activated: !comp.activated } : comp
    ));

    // 设置高亮边框（互斥）
    setHighlightedIndex(index);
  };

  // 处理鼠标悬停
  const handleMouseEnter = (_index: number) => {
    // 悬浮前置效果通过CSS z-index实现
  };

  return (
    <div style={{
      display: 'grid',
      gridTemplateRows: 'repeat(33, 1fr)',
      gridTemplateColumns: 'repeat(33, 1fr)',
      width: '90vh',
      height: '90vh',
      gap: '3px', // 增加组件间隔，提高可视性
      padding: '1vh'
    }}>
      {components.map((component, index) => (
        <FeatureA1
          key={index}
          activated={component.activated}
          highlighted={highlightedIndex === index}
          coordinates={coordinateMode ? component.coordinates : ''}
          onClick={() => handleComponentClick(index)}
          onMouseEnter={() => handleMouseEnter(index)}
        />
      ))}
    </div>
  );
};

export default LogicA1;
