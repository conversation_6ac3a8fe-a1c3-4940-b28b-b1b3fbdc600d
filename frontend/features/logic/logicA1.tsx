"use client";

import React, { useEffect, useRef } from 'react';
import { useMatrixStore } from '../../Store/store';
import FeatureA1 from '../feature/featureA1';

interface LogicA1Props {
  containerWidth: number;
  containerHeight: number;
}

const LogicA1: React.FC<LogicA1Props> = ({ containerWidth, containerHeight }) => {
  const { initialize, isInitialized } = useMatrixStore();
  const containerRef = useRef<HTMLDivElement>(null);

  // 网格配置
  const gridRows = 33;
  const gridCols = 33;
  
  // 监听初始化事件
  useEffect(() => {
    if (!isInitialized) {
      initialize();
    }
  }, [initialize, isInitialized]);

  // 生成坐标系统 (以中心组件为原点 '0,0')
  const generateCoordinates = () => {
    const coordinates: string[] = [];
    const centerRow = Math.floor(gridRows / 2); // 16 (0-based)
    const centerCol = Math.floor(gridCols / 2); // 16 (0-based)
    
    for (let row = 0; row < gridRows; row++) {
      for (let col = 0; col < gridCols; col++) {
        // 计算相对于中心的坐标
        const x = col - centerCol; // -16 到 +16
        const y = centerRow - row; // +16 到 -16 (y轴向上为正)
        coordinates.push(`${x},${y}`);
      }
    }
    
    return coordinates;
  };

  const coordinates = generateCoordinates();

  return (
    <div
      ref={containerRef}
      style={{
        width: containerWidth,
        height: containerHeight,
        // 网格排列
        display: 'grid',
        gridTemplateRows: `repeat(${gridRows}, 1fr)`,
        gridTemplateColumns: `repeat(${gridCols}, 1fr)`,
        // 移除默认的gap，使用组件自身的margin
        gap: 0,
        // 确保容器不会溢出
        overflow: 'hidden',
        // 相对定位
        position: 'relative',
      }}
    >
      {coordinates.map((coordinate, index) => (
        <FeatureA1
          key={coordinate}
          coordinate={coordinate}
          containerWidth={containerWidth}
          containerHeight={containerHeight}
        />
      ))}
    </div>
  );
};

export default LogicA1;
