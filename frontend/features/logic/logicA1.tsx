import React, { useState, useEffect, useCallback } from 'react';
import { useMatrixStore } from '../../Store/store';
import FeatureA1 from '../feature/featureA1';

interface ComponentState {
  isActive: boolean;
  isHighlighted: boolean;
}

const LogicA1: React.FC = () => {
  // 网格配置
  const GRID_ROWS = 33;
  const GRID_COLS = 33;
  const TOTAL_COMPONENTS = GRID_ROWS * GRID_COLS;

  // 从store获取状态
  const { coordinateMode, initializeMatrix } = useMatrixStore();

  // 组件状态管理
  const [componentStates, setComponentStates] = useState<ComponentState[]>(
    Array(TOTAL_COMPONENTS).fill(null).map(() => ({
      isActive: false,
      isHighlighted: false
    }))
  );

  // 悬停状态
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  // 监听初始化事件
  useEffect(() => {
    const handleInitialize = () => {
      setComponentStates(prev => 
        prev.map(() => ({
          isActive: false,
          isHighlighted: false
        }))
      );
    };

    // 监听store中的初始化事件
    // 这里我们通过coordinateMode的变化来触发初始化
    handleInitialize();
  }, [initializeMatrix]);

  // 计算组件坐标 (以中心组件为原点 '0,0')
  const getCoordinate = useCallback((index: number): string => {
    const row = Math.floor(index / GRID_COLS);
    const col = index % GRID_COLS;
    
    // 中心点坐标
    const centerRow = Math.floor(GRID_ROWS / 2);
    const centerCol = Math.floor(GRID_COLS / 2);
    
    // 计算相对坐标
    const x = col - centerCol;
    const y = centerRow - row; // Y轴向上为正
    
    return `${x},${y}`;
  }, []);

  // 处理组件点击
  const handleComponentClick = useCallback((index: number) => {
    setComponentStates(prev => {
      const newStates = [...prev];
      
      // 1. 激活状态(独立开关) - 切换当前组件的激活状态
      newStates[index].isActive = !newStates[index].isActive;
      
      // 2. 高亮边框(互斥单选) - 移除其他组件的高亮边框，设置当前组件高亮
      newStates.forEach((state, i) => {
        state.isHighlighted = i === index;
      });
      
      return newStates;
    });
  }, []);

  // 处理鼠标悬停
  const handleMouseEnter = useCallback((index: number) => {
    setHoveredIndex(index);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setHoveredIndex(null);
  }, []);

  // 计算组件尺寸和间隔
  const containerWidth = 95; // vh单位
  const containerHeight = 95; // vh单位
  const marginPercent = 0.05; // 组件间隔为容器宽度的0.05%
  
  // 计算实际的组件尺寸
  const componentWidth = `calc((${containerWidth}vh - ${GRID_COLS + 1} * ${containerWidth * marginPercent / 100}vh) / ${GRID_COLS})`;
  const componentHeight = `calc((${containerHeight}vh - ${GRID_ROWS + 1} * ${containerWidth * marginPercent / 100}vh) / ${GRID_ROWS})`;
  const margin = `${containerWidth * marginPercent / 100}vh`;

  return (
    <div
      style={{
        height: '95vh',
        width: '95vh',
        backgroundColor: '#6d6d6d',
        display: 'grid',
        // 网格排列: 33行33列
        gridTemplateRows: `repeat(${GRID_ROWS}, ${componentHeight})`,
        gridTemplateColumns: `repeat(${GRID_COLS}, ${componentWidth})`,
        // 组件间隔
        gap: margin,
        padding: margin,
        // 自动布局系统
        justifyContent: 'center',
        alignContent: 'center',
        overflow: 'hidden',
        boxSizing: 'border-box'
      }}
    >
      {Array.from({ length: TOTAL_COMPONENTS }, (_, index) => (
        <div
          key={index}
          style={{
            // 鼠标悬停效果: 悬浮前置，放大1.2倍
            transform: hoveredIndex === index ? 'scale(1.2)' : 'scale(1)',
            zIndex: hoveredIndex === index ? 10 : 1,
            transition: 'transform 0.2s ease-in-out',
            transformOrigin: 'center'
          }}
        >
          <FeatureA1
            isActive={componentStates[index].isActive}
            isHighlighted={componentStates[index].isHighlighted}
            coordinateText={getCoordinate(index)}
            showCoordinate={coordinateMode}
            onClick={() => handleComponentClick(index)}
            onMouseEnter={() => handleMouseEnter(index)}
            onMouseLeave={handleMouseLeave}
          />
        </div>
      ))}
    </div>
  );
};

export default LogicA1;
