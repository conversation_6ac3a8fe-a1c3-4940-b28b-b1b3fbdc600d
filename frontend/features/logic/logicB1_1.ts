// 矩阵功能业务逻辑
// 使用 Zustand 进行状态管理

import { create } from 'zustand';

interface MatrixState {
  // 坐标按键的状态
  coordinateMode: boolean;
  
  // 状态管理方法
  setCoordinateMode: (mode: boolean) => void;
  
  // 初始化方法
  initializeMatrix: () => void;
}

export const useMatrixStore = create<MatrixState>((set, get) => ({
  // 初始状态
  coordinateMode: false,
  
  // 设置坐标模式
  setCoordinateMode: (mode: boolean) => {
    set({ coordinateMode: mode });
  },
  
  // 初始化矩阵
  initializeMatrix: () => {
    // 重置坐标按键状态为false
    set({ coordinateMode: false });
    
    // 发出初始化事件（可以通过事件总线或其他方式通知其他组件）
    // 这里可以添加更多的初始化逻辑
    console.log('矩阵初始化完成');
  }
}));
