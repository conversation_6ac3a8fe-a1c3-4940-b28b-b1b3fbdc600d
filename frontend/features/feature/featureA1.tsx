import React from 'react';

interface FeatureA1Props {
  isActive: boolean;
  isHighlighted: boolean;
  coordinateText: string;
  showCoordinate: boolean;
  onClick: () => void;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}

const FeatureA1: React.FC<FeatureA1Props> = ({
  isActive,
  isHighlighted,
  coordinateText,
  showCoordinate,
  onClick,
  onMouseEnter,
  onMouseLeave
}) => {
  return (
    <div
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      style={{
        // 组件形状: 矩形
        // 组件高度和宽度由父组件计算传入
        height: '100%',
        width: '100%',
        // 背景颜色根据激活状态变化
        backgroundColor: isActive ? '#929292' : '#ffffff',
        // 组件圆角: 5px
        borderRadius: '5px',
        // 展示方式: 弹性布局
        display: 'flex',
        // 弹性方向: 垂直
        flexDirection: 'column',
        // 对齐方式: 水平，垂直居中
        justifyContent: 'center',
        alignItems: 'center',
        // 溢出处理: 隐藏
        overflow: 'hidden',
        // 组件定位: 相对定位
        position: 'relative',
        // 鼠标指针: 手型
        cursor: 'pointer',
        // 高亮边框样式 (互斥单选)
        border: isHighlighted ? '3px solid #ffd500' : '3px solid transparent',
        // 避免布局偏移 - 边框不影响元素尺寸
        boxSizing: 'border-box',
        // 过渡效果
        transition: 'all 0.2s ease-in-out'
      }}
    >
      {/* 坐标显示文本 */}
      {showCoordinate && (
        <span
          style={{
            // 字体大小: 随组件大小变化
            fontSize: 'clamp(8px, 1.5vw, 16px)',
            // 字体颜色: #242424
            color: '#242424',
            // 字体对齐: 居中
            textAlign: 'center',
            // 确保文本不被选中
            userSelect: 'none',
            // 文本不换行
            whiteSpace: 'nowrap'
          }}
        >
          {coordinateText}
        </span>
      )}
    </div>
  );
};

export default FeatureA1;
