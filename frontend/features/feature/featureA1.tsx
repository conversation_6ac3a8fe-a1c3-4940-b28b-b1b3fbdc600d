'use client'

import React, { useState } from 'react';

interface FeatureA1Props {
  activated: boolean;
  highlighted: boolean;
  coordinates: string;
  onClick: () => void;
  onMouseEnter: () => void;
}

const FeatureA1: React.FC<FeatureA1Props> = ({
  activated,
  highlighted,
  coordinates,
  onClick,
  onMouseEnter
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getBackgroundColor = () => {
    if (activated) return '#929292';
    return '#ffffff';
  };

  const getBorderStyle = () => {
    if (highlighted) {
      return {
        border: '3px solid #ffd500',
        boxSizing: 'border-box' as const
      };
    }
    return {
      border: '3px solid transparent',
      boxSizing: 'border-box' as const
    };
  };

  return (
    <div
      style={{
        backgroundColor: getBackgroundColor(),
        borderRadius: '5px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
        position: 'relative',
        cursor: 'pointer',
        transform: isHovered ? 'scale(1.2)' : 'scale(1)',
        zIndex: isHovered ? 10 : 1,
        transition: 'transform 0.2s ease, z-index 0.2s ease',
        width: '100%',
        height: '100%',
        ...getBorderStyle()
      }}
      onClick={onClick}
      onMouseEnter={() => {
        setIsHovered(true);
        onMouseEnter();
      }}
      onMouseLeave={() => {
        setIsHovered(false);
      }}
    >
      {coordinates && (
        <span style={{
          fontSize: 'calc(0.5vw + 0.5vh)',
          color: '#242424',
          textAlign: 'center',
          userSelect: 'none'
        }}>
          {coordinates}
        </span>
      )}
    </div>
  );
};

export default FeatureA1;
