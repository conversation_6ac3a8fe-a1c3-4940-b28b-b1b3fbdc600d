"use client";

import React from 'react';
import { useMatrixStore } from '../../Store/store';

interface FeatureA1Props {
  coordinate: string;
  containerWidth: number;
  containerHeight: number;
}

const FeatureA1: React.FC<FeatureA1Props> = ({ coordinate, containerWidth, containerHeight }) => {
  const {
    coordinateDisplay,
    activatedComponents,
    highlightedComponent,
    toggleComponentActivation,
    setHighlightedComponent
  } = useMatrixStore();

  // 计算组件尺寸
  const gridRows = 33;
  const gridCols = 33;
  const margin = containerWidth * 0.005; // 0.5%
  
  const componentHeight = (containerHeight - (gridRows + 1) * margin) / gridRows;
  const componentWidth = (containerWidth - (gridCols + 1) * margin) / gridCols;
  
  // 检查激活状态
  const isActivated = activatedComponents.has(coordinate);
  const isHighlighted = highlightedComponent === coordinate;
  
  // 计算字体大小 (随组件大小变化)
  const fontSize = Math.min(componentWidth, componentHeight) * 0.2;
  
  const handleClick = () => {
    // 切换激活状态
    toggleComponentActivation(coordinate);
    // 设置高亮边框 (互斥单选)
    setHighlightedComponent(coordinate);
  };

  return (
    <div
      onClick={handleClick}
      style={{
        // 组件形状: 矩形
        width: componentWidth,
        height: componentHeight,
        
        // 背景颜色
        backgroundColor: isActivated ? '#929292' : '#cecece',
        
        // 组件圆角
        borderRadius: '5px',
        
        // 展示方式: 弹性布局
        display: 'flex',
        // 弹性方向: 垂直
        flexDirection: 'column',
        // 对齐方式: 水平，垂直居中
        justifyContent: 'center',
        alignItems: 'center',
        
        // 溢出处理: 隐藏
        overflow: 'hidden',
        
        // 组件定位: 相对定位
        position: 'relative',
        
        // 鼠标指针: 手型
        cursor: 'pointer',
        
        // 高亮边框
        border: isHighlighted ? '3px solid #ffd500' : 'none',
        
        // 组件间隔
        margin: margin,
        
        // 鼠标悬停效果
        transition: 'transform 0.2s ease, z-index 0.2s ease',
        zIndex: 1,
        
        // 悬浮前置和放大效果通过CSS hover实现
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'scale(1.2)';
        e.currentTarget.style.zIndex = '10';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'scale(1)';
        e.currentTarget.style.zIndex = '1';
      }}
    >
      {/* 坐标显示 */}
      {coordinateDisplay && (
        <span
          style={{
            // 字体大小: 随组件大小变化
            fontSize: `${fontSize}px`,
            // 字体颜色
            color: '#242424',
            // 字体对齐: 居中
            textAlign: 'center',
            // 确保文字不会被选中
            userSelect: 'none',
            // 文字不换行
            whiteSpace: 'nowrap',
          }}
        >
          {coordinate}
        </span>
      )}
    </div>
  );
};

export default FeatureA1;
