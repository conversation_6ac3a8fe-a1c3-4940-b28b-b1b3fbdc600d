'use client';

import React from 'react';
import SecondaryButton from '../../ButtonCodes/secondary/SecondaryButton/SecondaryButton';
import { useMatrixStore } from '../logic/logicB1_1';

const FeatureB1_1: React.FC = () => {
  const { coordinateMode, setCoordinateMode, initializeMatrix } = useMatrixStore();

  // 处理初始化按键点击
  const handleInitialize = () => {
    initializeMatrix();
  };

  // 处理坐标按键切换
  const handleCoordinateToggle = (isActive: boolean) => {
    setCoordinateMode(isActive);
  };

  return (
    <div style={{
      // 容器样式
      position: 'relative',
      height: '15%',
      width: '94%',
      backgroundColor: '#bebebe',
      overflow: 'hidden',
      top: '6%'
    }}>
      {/* 默认文本 */}
      <div style={{
        position: 'absolute',
        top: '20%',
        left: '1%',
        fontSize: '25px',
        color: '#242424'
      }}>
        矩阵
      </div>

      {/* 初始化按键 */}
      <div style={{
        position: 'absolute',
        top: '55%',
        left: '4%',
        height: '35%',
        width: '44%'
      }}>
        <SecondaryButton
          text="初始化"
          mode="instant"
          onClick={handleInitialize}
          style={{
            height: '100%',
            width: '100%',
            fontSize: 'auto' // 尺寸自适应
          }}
        />
      </div>

      {/* 坐标按键 */}
      <div style={{
        position: 'absolute',
        top: '55%',
        right: '4%',
        height: '35%',
        width: '44%'
      }}>
        <SecondaryButton
          text="坐标"
          mode="toggle"
          isActive={coordinateMode}
          onToggle={handleCoordinateToggle}
          style={{
            height: '100%',
            width: '100%',
            fontSize: 'auto' // 尺寸自适应
          }}
        />
      </div>
    </div>
  );
};

export default FeatureB1_1;
