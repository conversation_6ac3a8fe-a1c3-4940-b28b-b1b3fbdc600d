import React, { useRef, useEffect, useState } from 'react';
import LogicA1 from '../../features/logic/logicA1';

const ComponentA1: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });

  // 监听容器尺寸变化
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setContainerDimensions({ width: offsetWidth, height: offsetHeight });
      }
    };

    // 初始化尺寸
    updateDimensions();

    // 监听窗口大小变化
    window.addEventListener('resize', updateDimensions);

    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      style={{
        height: '95vh',
        width: '95vh',
        backgroundColor: '#6d6d6d',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
        position: 'relative'
      }}
    >
      {/* 装载容器: componetA1 */}
      {/* 存放组件: featureA1 */}
      {/* 排列方式: 网格排列 (33x33) */}
      {containerDimensions.width > 0 && containerDimensions.height > 0 && (
        <LogicA1
          containerWidth={containerDimensions.width}
          containerHeight={containerDimensions.height}
        />
      )}
    </div>
  );
};

export default ComponentA1;
